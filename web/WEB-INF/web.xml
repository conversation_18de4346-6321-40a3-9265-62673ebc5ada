<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <display-name>Student Activity Application System</display-name>

    <!-- 欢迎页面 -->
    <welcome-file-list>
        <welcome-file>login.jsp</welcome-file>
    </welcome-file-list>

    <!-- 学生登录Servlet -->
    <servlet>
        <servlet-name>LoginServlet</servlet-name>
        <servlet-class>com.student.servlet.LoginServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LoginServlet</servlet-name>
        <url-pattern>/login</url-pattern>
    </servlet-mapping>

    <!-- 学生注册Servlet -->
    <servlet>
        <servlet-name>RegisterServlet</servlet-name>
        <servlet-class>com.student.servlet.RegisterServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RegisterServlet</servlet-name>
        <url-pattern>/register</url-pattern>
    </servlet-mapping>

    <!-- 活动列表Servlet -->
    <servlet>
        <servlet-name>ActivityListServlet</servlet-name>
        <servlet-class>com.student.servlet.ActivityListServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ActivityListServlet</servlet-name>
        <url-pattern>/activities</url-pattern>
    </servlet-mapping>

    <!-- 活动申请Servlet -->
    <servlet>
        <servlet-name>ApplyServlet</servlet-name>
        <servlet-class>com.student.servlet.ApplyServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ApplyServlet</servlet-name>
        <url-pattern>/apply</url-pattern>
    </servlet-mapping>

    <!-- 申请状态查询Servlet -->
    <servlet>
        <servlet-name>ApplicationStatusServlet</servlet-name>
        <servlet-class>com.student.servlet.ApplicationStatusServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ApplicationStatusServlet</servlet-name>
        <url-pattern>/status</url-pattern>
    </servlet-mapping>

    <!-- 字符编码过滤器 -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>com.student.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

</web-app>