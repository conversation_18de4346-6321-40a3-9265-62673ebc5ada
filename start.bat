@echo off
echo ========================================
echo 学生活动申请系统启动脚本
echo ========================================
echo.

echo 正在检查环境...

:: 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Java环境，请先安装JDK 8或更高版本
    pause
    exit /b 1
)
echo [✓] Java环境检查通过

:: 检查MySQL服务
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] MySQL服务未启动，正在尝试启动...
    net start mysql >nul 2>&1
    if %errorlevel% neq 0 (
        echo [错误] MySQL服务启动失败，请手动启动MySQL服务
        pause
        exit /b 1
    )
)
echo [✓] MySQL服务检查通过

echo.
echo ========================================
echo 部署前检查清单
echo ========================================
echo 1. 确保已执行database/init.sql初始化数据库
echo 2. 确保web/WEB-INF/lib/目录下有mysql-connector-java.jar
echo 3. 确保Tomcat服务器已配置
echo 4. 确保数据库连接配置正确(DBUtil.java)
echo.

echo 按任意键继续，或按Ctrl+C取消...
pause >nul

echo.
echo ========================================
echo 系统信息
echo ========================================
echo 项目名称: 学生活动申请系统
echo 访问地址: http://localhost:8080/
echo 测试账号: 
echo   学号: 2021001, 密码: 123456 (张三)
echo   学号: 2021002, 密码: 123456 (李四)
echo   学号: 2021003, 密码: 123456 (王五)
echo.
echo 如果遇到404错误，请检查:
echo 1. Tomcat是否正常启动
echo 2. 项目是否正确部署
echo 3. web.xml配置是否正确
echo.
echo ========================================
echo 启动完成！请在IDE中启动Tomcat服务器
echo ========================================

pause
