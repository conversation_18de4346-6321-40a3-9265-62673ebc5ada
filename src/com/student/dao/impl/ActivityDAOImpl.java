package com.student.dao.impl;

import com.student.dao.ActivityDAO;
import com.student.entity.Activity;
import com.student.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动数据访问实现类
 */
public class ActivityDAOImpl implements ActivityDAO {
    
    @Override
    public List<Activity> findOpenActivities() {
        List<Activity> activities = new ArrayList<>();
        String sql = "SELECT * FROM activities WHERE status = 'OPEN' ORDER BY start_time";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                activities.add(mapResultSetToActivity(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return activities;
    }
    
    @Override
    public Activity findById(int id) {
        String sql = "SELECT * FROM activities WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToActivity(rs);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    @Override
    public boolean addActivity(Activity activity) {
        String sql = "INSERT INTO activities (name, description, start_time, end_time, location, max_participants, current_participants, organizer, status, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, activity.getName());
            pstmt.setString(2, activity.getDescription());
            pstmt.setTimestamp(3, new Timestamp(activity.getStartTime().getTime()));
            pstmt.setTimestamp(4, new Timestamp(activity.getEndTime().getTime()));
            pstmt.setString(5, activity.getLocation());
            pstmt.setInt(6, activity.getMaxParticipants());
            pstmt.setInt(7, activity.getCurrentParticipants());
            pstmt.setString(8, activity.getOrganizer());
            pstmt.setString(9, activity.getStatus());
            pstmt.setTimestamp(10, new Timestamp(activity.getCreateTime().getTime()));
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    @Override
    public boolean updateActivity(Activity activity) {
        String sql = "UPDATE activities SET name = ?, description = ?, start_time = ?, end_time = ?, location = ?, max_participants = ?, organizer = ?, status = ? WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, activity.getName());
            pstmt.setString(2, activity.getDescription());
            pstmt.setTimestamp(3, new Timestamp(activity.getStartTime().getTime()));
            pstmt.setTimestamp(4, new Timestamp(activity.getEndTime().getTime()));
            pstmt.setString(5, activity.getLocation());
            pstmt.setInt(6, activity.getMaxParticipants());
            pstmt.setString(7, activity.getOrganizer());
            pstmt.setString(8, activity.getStatus());
            pstmt.setInt(9, activity.getId());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    @Override
    public boolean updateParticipantCount(int activityId, int count) {
        String sql = "UPDATE activities SET current_participants = ? WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, count);
            pstmt.setInt(2, activityId);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    @Override
    public List<Activity> findAll() {
        List<Activity> activities = new ArrayList<>();
        String sql = "SELECT * FROM activities ORDER BY create_time DESC";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                activities.add(mapResultSetToActivity(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return activities;
    }
    
    private Activity mapResultSetToActivity(ResultSet rs) throws SQLException {
        Activity activity = new Activity();
        activity.setId(rs.getInt("id"));
        activity.setName(rs.getString("name"));
        activity.setDescription(rs.getString("description"));
        activity.setStartTime(rs.getTimestamp("start_time"));
        activity.setEndTime(rs.getTimestamp("end_time"));
        activity.setLocation(rs.getString("location"));
        activity.setMaxParticipants(rs.getInt("max_participants"));
        activity.setCurrentParticipants(rs.getInt("current_participants"));
        activity.setOrganizer(rs.getString("organizer"));
        activity.setStatus(rs.getString("status"));
        activity.setCreateTime(rs.getTimestamp("create_time"));
        return activity;
    }
}
