package com.student.dao;

import com.student.entity.Activity;
import java.util.List;

/**
 * 活动数据访问接口
 */
public interface ActivityDAO {
    /**
     * 查询所有开放的活动
     */
    List<Activity> findOpenActivities();
    
    /**
     * 根据ID查询活动
     */
    Activity findById(int id);
    
    /**
     * 添加活动
     */
    boolean addActivity(Activity activity);
    
    /**
     * 更新活动信息
     */
    boolean updateActivity(Activity activity);
    
    /**
     * 更新活动参与人数
     */
    boolean updateParticipantCount(int activityId, int count);
    
    /**
     * 查询所有活动
     */
    List<Activity> findAll();
}
