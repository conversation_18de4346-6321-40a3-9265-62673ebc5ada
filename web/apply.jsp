<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.student.entity.Activity" %>
<%@ page import="com.student.entity.Student" %>
<%@ page import="java.text.SimpleDateFormat" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>学生活动申请系统 - 申请活动</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        .nav {
            background-color: #0056b3;
            padding: 10px 0;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            margin: 0 20px;
            padding: 10px 15px;
            border-radius: 5px;
        }
        .nav a:hover {
            background-color: #004085;
        }
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .apply-container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .apply-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .apply-header h1 {
            color: #007bff;
            margin-bottom: 10px;
        }
        .activity-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .activity-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .info-item {
            display: flex;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            margin-right: 10px;
            color: #666;
            min-width: 80px;
        }
        .activity-description {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            margin-top: 15px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
            font-size: 16px;
        }
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            font-family: Arial, sans-serif;
            resize: vertical;
            min-height: 120px;
            box-sizing: border-box;
        }
        .form-group textarea:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }
        .char-count {
            text-align: right;
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>学生活动申请系统</h1>
    </div>
    
    <div class="nav">
        <a href="<%= request.getContextPath() %>/activities">活动列表</a>
        <a href="<%= request.getContextPath() %>/status">我的申请</a>
        <a href="<%= request.getContextPath() %>/login">退出登录</a>
    </div>
    
    <div class="container">
        <%
            Activity activity = (Activity) request.getAttribute("activity");
            Student student = (Student) request.getAttribute("student");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        %>
        
        <div class="apply-container">
            <div class="apply-header">
                <h1>申请活动</h1>
                <p>请仔细填写申请信息</p>
            </div>
            
            <% if (request.getAttribute("error") != null) { %>
                <div class="error">
                    <%= request.getAttribute("error") %>
                </div>
            <% } %>
            
            <div class="activity-info">
                <div class="activity-title"><%= activity.getName() %></div>
                
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">开始时间:</span>
                        <span><%= sdf.format(activity.getStartTime()) %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">结束时间:</span>
                        <span><%= sdf.format(activity.getEndTime()) %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">活动地点:</span>
                        <span><%= activity.getLocation() %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">主办方:</span>
                        <span><%= activity.getOrganizer() %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">参与人数:</span>
                        <span><%= activity.getCurrentParticipants() %>/<%= activity.getMaxParticipants() %></span>
                    </div>
                </div>
                
                <div class="activity-description">
                    <strong>活动描述：</strong><br>
                    <%= activity.getDescription() %>
                </div>
            </div>
            
            <form action="<%= request.getContextPath() %>/apply" method="post">
                <input type="hidden" name="activityId" value="<%= activity.getId() %>">
                
                <div class="form-group">
                    <label for="reason">申请理由 <span class="required">*</span>:</label>
                    <textarea id="reason" name="reason" required 
                              placeholder="请详细说明您申请参加此活动的理由，包括您的兴趣、相关经验或期望收获等..."
                              maxlength="500" onkeyup="updateCharCount()"></textarea>
                    <div class="char-count">
                        <span id="charCount">0</span>/500 字符
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">提交申请</button>
                    <a href="<%= request.getContextPath() %>/activities" class="btn btn-secondary">返回列表</a>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function updateCharCount() {
            const textarea = document.getElementById('reason');
            const charCount = document.getElementById('charCount');
            charCount.textContent = textarea.value.length;
            
            if (textarea.value.length > 450) {
                charCount.style.color = '#dc3545';
            } else {
                charCount.style.color = '#666';
            }
        }
        
        // 页面加载时初始化字符计数
        document.addEventListener('DOMContentLoaded', function() {
            updateCharCount();
        });
    </script>
</body>
</html>
