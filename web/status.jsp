<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.student.entity.Application" %>
<%@ page import="com.student.entity.Student" %>
<%@ page import="java.text.SimpleDateFormat" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>学生活动申请系统 - 申请状态</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        .nav {
            background-color: #0056b3;
            padding: 10px 0;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            margin: 0 20px;
            padding: 10px 15px;
            border-radius: 5px;
        }
        .nav a:hover {
            background-color: #004085;
        }
        .nav a.active {
            background-color: #004085;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .welcome {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .application-card {
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid #007bff;
        }
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .application-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status-approved {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .application-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            display: flex;
            flex-direction: column;
        }
        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .info-value {
            color: #333;
            font-size: 16px;
        }
        .application-reason {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .reason-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .reason-content {
            line-height: 1.6;
            color: #555;
        }
        .review-comment {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #6c757d;
        }
        .review-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .review-content {
            line-height: 1.6;
            color: #555;
        }
        .no-applications {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .no-applications h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>学生活动申请系统</h1>
    </div>
    
    <div class="nav">
        <a href="<%= request.getContextPath() %>/activities">活动列表</a>
        <a href="<%= request.getContextPath() %>/status" class="active">我的申请</a>
        <a href="<%= request.getContextPath() %>/login">退出登录</a>
    </div>
    
    <div class="container">
        <%
            Student student = (Student) request.getAttribute("student");
            List<Application> applications = (List<Application>) request.getAttribute("applications");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            
            // 统计申请状态
            int totalCount = applications != null ? applications.size() : 0;
            int pendingCount = 0;
            int approvedCount = 0;
            int rejectedCount = 0;
            
            if (applications != null) {
                for (Application app : applications) {
                    switch (app.getStatus()) {
                        case "PENDING": pendingCount++; break;
                        case "APPROVED": approvedCount++; break;
                        case "REJECTED": rejectedCount++; break;
                    }
                }
            }
        %>
        
        <div class="welcome">
            <h2><%= student.getName() %> 的申请记录</h2>
            <p>学号：<%= student.getStudentId() %> | 专业：<%= student.getMajor() %> | 年级：<%= student.getGrade() %></p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><%= totalCount %></div>
                <div class="stat-label">总申请数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><%= pendingCount %></div>
                <div class="stat-label">待审核</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><%= approvedCount %></div>
                <div class="stat-label">已通过</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><%= rejectedCount %></div>
                <div class="stat-label">已拒绝</div>
            </div>
        </div>
        
        <% if (applications != null && !applications.isEmpty()) { %>
            <% for (Application application : applications) { %>
                <div class="application-card">
                    <div class="application-header">
                        <h3 class="application-title">申请ID: #<%= application.getId() %></h3>
                        <% 
                            String statusClass = "";
                            String statusText = "";
                            switch (application.getStatus()) {
                                case "PENDING":
                                    statusClass = "status-pending";
                                    statusText = "待审核";
                                    break;
                                case "APPROVED":
                                    statusClass = "status-approved";
                                    statusText = "已通过";
                                    break;
                                case "REJECTED":
                                    statusClass = "status-rejected";
                                    statusText = "已拒绝";
                                    break;
                                default:
                                    statusClass = "status-pending";
                                    statusText = application.getStatus();
                            }
                        %>
                        <span class="status-badge <%= statusClass %>"><%= statusText %></span>
                    </div>
                    
                    <div class="application-info">
                        <div class="info-item">
                            <span class="info-label">活动ID</span>
                            <span class="info-value">#<%= application.getActivityId() %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">申请时间</span>
                            <span class="info-value"><%= sdf.format(application.getApplyTime()) %></span>
                        </div>
                        <% if (application.getReviewTime() != null) { %>
                        <div class="info-item">
                            <span class="info-label">审核时间</span>
                            <span class="info-value"><%= sdf.format(application.getReviewTime()) %></span>
                        </div>
                        <% } %>
                    </div>
                    
                    <div class="application-reason">
                        <div class="reason-title">申请理由：</div>
                        <div class="reason-content"><%= application.getReason() %></div>
                    </div>
                    
                    <% if (application.getReviewComment() != null && !application.getReviewComment().trim().isEmpty()) { %>
                    <div class="review-comment">
                        <div class="review-title">审核意见：</div>
                        <div class="review-content"><%= application.getReviewComment() %></div>
                    </div>
                    <% } %>
                </div>
            <% } %>
        <% } else { %>
            <div class="application-card">
                <div class="no-applications">
                    <h3>暂无申请记录</h3>
                    <p>您还没有申请任何活动</p>
                    <a href="<%= request.getContextPath() %>/activities" class="btn">浏览活动</a>
                </div>
            </div>
        <% } %>
    </div>
</body>
</html>
