package com.student.dao.impl;

import com.student.dao.ApplicationDAO;
import com.student.entity.Application;
import com.student.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 申请数据访问实现类
 */
public class ApplicationDAOImpl implements ApplicationDAO {
    
    @Override
    public boolean addApplication(Application application) {
        String sql = "INSERT INTO applications (student_id, activity_id, reason, status, apply_time) VALUES (?, ?, ?, ?, ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, application.getStudentId());
            pstmt.setInt(2, application.getActivityId());
            pstmt.setString(3, application.getReason());
            pstmt.setString(4, application.getStatus());
            pstmt.setTimestamp(5, new Timestamp(application.getApplyTime().getTime()));
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    @Override
    public List<Application> findByStudentId(int studentId) {
        List<Application> applications = new ArrayList<>();
        String sql = "SELECT a.*, ac.name as activity_name, ac.start_time, ac.location " +
                    "FROM applications a " +
                    "JOIN activities ac ON a.activity_id = ac.id " +
                    "WHERE a.student_id = ? " +
                    "ORDER BY a.apply_time DESC";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, studentId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    applications.add(mapResultSetToApplication(rs));
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return applications;
    }
    
    @Override
    public List<Application> findByActivityId(int activityId) {
        List<Application> applications = new ArrayList<>();
        String sql = "SELECT a.*, s.name as student_name, s.student_id as student_number " +
                    "FROM applications a " +
                    "JOIN students s ON a.student_id = s.id " +
                    "WHERE a.activity_id = ? " +
                    "ORDER BY a.apply_time DESC";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, activityId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    applications.add(mapResultSetToApplication(rs));
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return applications;
    }
    
    @Override
    public boolean hasApplied(int studentId, int activityId) {
        String sql = "SELECT COUNT(*) FROM applications WHERE student_id = ? AND activity_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, studentId);
            pstmt.setInt(2, activityId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    @Override
    public boolean updateApplicationStatus(int applicationId, String status, String reviewComment) {
        String sql = "UPDATE applications SET status = ?, review_comment = ?, review_time = ? WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, status);
            pstmt.setString(2, reviewComment);
            pstmt.setTimestamp(3, new Timestamp(System.currentTimeMillis()));
            pstmt.setInt(4, applicationId);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    @Override
    public Application findById(int id) {
        String sql = "SELECT * FROM applications WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToApplication(rs);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    @Override
    public List<Application> findAll() {
        List<Application> applications = new ArrayList<>();
        String sql = "SELECT * FROM applications ORDER BY apply_time DESC";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                applications.add(mapResultSetToApplication(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return applications;
    }
    
    @Override
    public List<Application> findPendingApplications() {
        List<Application> applications = new ArrayList<>();
        String sql = "SELECT * FROM applications WHERE status = 'PENDING' ORDER BY apply_time";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                applications.add(mapResultSetToApplication(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return applications;
    }
    
    private Application mapResultSetToApplication(ResultSet rs) throws SQLException {
        Application application = new Application();
        application.setId(rs.getInt("id"));
        application.setStudentId(rs.getInt("student_id"));
        application.setActivityId(rs.getInt("activity_id"));
        application.setReason(rs.getString("reason"));
        application.setStatus(rs.getString("status"));
        application.setApplyTime(rs.getTimestamp("apply_time"));
        
        Timestamp reviewTime = rs.getTimestamp("review_time");
        if (reviewTime != null) {
            application.setReviewTime(reviewTime);
        }
        
        application.setReviewComment(rs.getString("review_comment"));
        return application;
    }
}
