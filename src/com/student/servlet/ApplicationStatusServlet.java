package com.student.servlet;

import com.student.dao.ApplicationDAO;
import com.student.dao.impl.ApplicationDAOImpl;
import com.student.entity.Application;
import com.student.entity.Student;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 申请状态查询Servlet
 */
public class ApplicationStatusServlet extends HttpServlet {
    private ApplicationDAO applicationDAO = new ApplicationDAOImpl();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // 检查用户是否登录
        HttpSession session = request.getSession();
        Student student = (Student) session.getAttribute("student");
        
        if (student == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        // 获取该学生的所有申请
        List<Application> applications = applicationDAO.findByStudentId(student.getId());
        
        request.setAttribute("applications", applications);
        request.setAttribute("student", student);
        
        // 转发到申请状态页面
        request.getRequestDispatcher("/status.jsp").forward(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
