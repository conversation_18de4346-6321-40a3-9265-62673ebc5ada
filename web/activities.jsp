<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.student.entity.Activity" %>
<%@ page import="com.student.entity.Student" %>
<%@ page import="java.text.SimpleDateFormat" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>学生活动申请系统 - 活动列表</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        .nav {
            background-color: #0056b3;
            padding: 10px 0;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            margin: 0 20px;
            padding: 10px 15px;
            border-radius: 5px;
        }
        .nav a:hover {
            background-color: #004085;
        }
        .nav a.active {
            background-color: #004085;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .welcome {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .activity-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .activity-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
        .activity-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .status-open {
            background-color: #d4edda;
            color: #155724;
        }
        .status-full {
            background-color: #f8d7da;
            color: #721c24;
        }
        .activity-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .info-item {
            display: flex;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            margin-right: 10px;
            color: #666;
        }
        .activity-description {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        .activity-actions {
            text-align: right;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-disabled {
            background-color: #6c757d;
            color: white;
            cursor: not-allowed;
        }
        .no-activities {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>学生活动申请系统</h1>
    </div>
    
    <div class="nav">
        <a href="<%= request.getContextPath() %>/activities" class="active">活动列表</a>
        <a href="<%= request.getContextPath() %>/status">我的申请</a>
        <a href="<%= request.getContextPath() %>/login">退出登录</a>
    </div>
    
    <div class="container">
        <%
            Student student = (Student) request.getAttribute("student");
            List<Activity> activities = (List<Activity>) request.getAttribute("activities");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        %>
        
        <div class="welcome">
            <h2>欢迎，<%= student.getName() %>！</h2>
            <p>学号：<%= student.getStudentId() %> | 专业：<%= student.getMajor() %> | 年级：<%= student.getGrade() %></p>
        </div>
        
        <% if (activities != null && !activities.isEmpty()) { %>
            <% for (Activity activity : activities) { %>
                <div class="activity-card">
                    <div class="activity-header">
                        <h3 class="activity-title"><%= activity.getName() %></h3>
                        <% if (activity.getCurrentParticipants() >= activity.getMaxParticipants()) { %>
                            <span class="activity-status status-full">名额已满</span>
                        <% } else { %>
                            <span class="activity-status status-open">报名中</span>
                        <% } %>
                    </div>
                    
                    <div class="activity-info">
                        <div class="info-item">
                            <span class="info-label">开始时间:</span>
                            <span><%= sdf.format(activity.getStartTime()) %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">结束时间:</span>
                            <span><%= sdf.format(activity.getEndTime()) %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">活动地点:</span>
                            <span><%= activity.getLocation() %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">主办方:</span>
                            <span><%= activity.getOrganizer() %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">参与人数:</span>
                            <span><%= activity.getCurrentParticipants() %>/<%= activity.getMaxParticipants() %></span>
                        </div>
                    </div>
                    
                    <div class="activity-description">
                        <strong>活动描述：</strong><br>
                        <%= activity.getDescription() %>
                    </div>
                    
                    <div class="activity-actions">
                        <% if (activity.getCurrentParticipants() >= activity.getMaxParticipants()) { %>
                            <span class="btn btn-disabled">名额已满</span>
                        <% } else { %>
                            <a href="<%= request.getContextPath() %>/apply?activityId=<%= activity.getId() %>" 
                               class="btn btn-primary">立即申请</a>
                        <% } %>
                    </div>
                </div>
            <% } %>
        <% } else { %>
            <div class="activity-card">
                <div class="no-activities">
                    <h3>暂无可申请的活动</h3>
                    <p>请稍后再来查看</p>
                </div>
            </div>
        <% } %>
    </div>
</body>
</html>
