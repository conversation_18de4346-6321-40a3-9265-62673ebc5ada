<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.sql.*" %>
<%@ page import="com.student.util.DBUtil" %>
<%@ page import="com.student.dao.impl.StudentDAOImpl" %>
<%@ page import="com.student.entity.Student" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>数据库调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>数据库连接和数据调试</h1>
    
    <div class="section">
        <h2>1. 数据库连接测试</h2>
        <%
            try (Connection conn = DBUtil.getConnection()) {
                out.println("<p class='success'>✓ 数据库连接成功！</p>");
                out.println("<p class='info'>数据库URL: " + conn.getMetaData().getURL() + "</p>");
                out.println("<p class='info'>数据库用户: " + conn.getMetaData().getUserName() + "</p>");
            } catch (Exception e) {
                out.println("<p class='error'>✗ 数据库连接失败：" + e.getMessage() + "</p>");
                e.printStackTrace();
            }
        %>
    </div>
    
    <div class="section">
        <h2>2. 检查数据库和表是否存在</h2>
        <%
            try (Connection conn = DBUtil.getConnection()) {
                // 检查数据库
                String dbName = conn.getCatalog();
                out.println("<p class='info'>当前数据库: " + dbName + "</p>");
                
                // 检查表是否存在
                DatabaseMetaData metaData = conn.getMetaData();
                ResultSet tables = metaData.getTables(null, null, "students", null);
                if (tables.next()) {
                    out.println("<p class='success'>✓ students表存在</p>");
                } else {
                    out.println("<p class='error'>✗ students表不存在！</p>");
                }
                tables.close();
                
            } catch (Exception e) {
                out.println("<p class='error'>检查失败：" + e.getMessage() + "</p>");
            }
        %>
    </div>
    
    <div class="section">
        <h2>3. 学生表数据统计</h2>
        <%
            try (Connection conn = DBUtil.getConnection()) {
                String countSql = "SELECT COUNT(*) as count FROM students";
                try (PreparedStatement pstmt = conn.prepareStatement(countSql);
                     ResultSet rs = pstmt.executeQuery()) {
                    if (rs.next()) {
                        int count = rs.getInt("count");
                        out.println("<p class='info'>学生表中共有 " + count + " 条记录</p>");
                    }
                }
            } catch (Exception e) {
                out.println("<p class='error'>统计失败：" + e.getMessage() + "</p>");
            }
        %>
    </div>
    
    <div class="section">
        <h2>4. 显示所有学生数据</h2>
        <table>
            <tr>
                <th>ID</th>
                <th>学号</th>
                <th>姓名</th>
                <th>密码</th>
                <th>邮箱</th>
                <th>专业</th>
                <th>年级</th>
            </tr>
            <%
                try (Connection conn = DBUtil.getConnection()) {
                    String sql = "SELECT * FROM students ORDER BY id";
                    try (PreparedStatement pstmt = conn.prepareStatement(sql);
                         ResultSet rs = pstmt.executeQuery()) {
                        
                        boolean hasData = false;
                        while (rs.next()) {
                            hasData = true;
                            out.println("<tr>");
                            out.println("<td>" + rs.getInt("id") + "</td>");
                            out.println("<td>" + rs.getString("student_id") + "</td>");
                            out.println("<td>" + rs.getString("name") + "</td>");
                            out.println("<td>" + rs.getString("password") + "</td>");
                            out.println("<td>" + rs.getString("email") + "</td>");
                            out.println("<td>" + rs.getString("major") + "</td>");
                            out.println("<td>" + rs.getString("grade") + "</td>");
                            out.println("</tr>");
                        }
                        
                        if (!hasData) {
                            out.println("<tr><td colspan='7' class='error'>没有找到任何学生数据！</td></tr>");
                        }
                    }
                } catch (Exception e) {
                    out.println("<tr><td colspan='7' class='error'>查询失败：" + e.getMessage() + "</td></tr>");
                }
            %>
        </table>
    </div>
    
    <div class="section">
        <h2>5. 测试DAO查询</h2>
        <%
            try {
                StudentDAOImpl studentDAO = new StudentDAOImpl();
                
                // 测试根据学号查询
                Student student = studentDAO.findByStudentId("2021001");
                if (student != null) {
                    out.println("<p class='success'>✓ DAO查询成功找到学生：" + student.getName() + "</p>");
                } else {
                    out.println("<p class='error'>✗ DAO查询未找到学号为2021001的学生</p>");
                }
                
                // 测试登录验证
                Student loginStudent = studentDAO.findByStudentIdAndPassword("2021001", "123456");
                if (loginStudent != null) {
                    out.println("<p class='success'>✓ 登录验证成功：" + loginStudent.getName() + "</p>");
                } else {
                    out.println("<p class='error'>✗ 登录验证失败：学号2021001，密码123456</p>");
                }
                
            } catch (Exception e) {
                out.println("<p class='error'>DAO测试失败：" + e.getMessage() + "</p>");
                e.printStackTrace();
            }
        %>
    </div>
    
    <div class="section">
        <h2>6. 手动执行初始化脚本建议</h2>
        <p>如果上面显示没有数据，请执行以下步骤：</p>
        <ol>
            <li>打开MySQL命令行或MySQL Workbench</li>
            <li>执行以下命令：</li>
        </ol>
        <pre style="background-color: #f5f5f5; padding: 10px; border-radius: 5px;">
-- 1. 创建数据库
CREATE DATABASE IF NOT EXISTS student_activity DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 使用数据库
USE student_activity;

-- 3. 插入测试学生数据
INSERT INTO students (student_id, name, password, email, phone, major, grade) VALUES
('2021001', '张三', '123456', '<EMAIL>', '13800138001', '计算机科学与技术', '大三'),
('2021002', '李四', '123456', '<EMAIL>', '13800138002', '软件工程', '大三'),
('2021003', '王五', '123456', '<EMAIL>', '13800138003', '信息管理与信息系统', '大二');
        </pre>
    </div>
    
    <p><a href="login.jsp">返回登录页面</a></p>
</body>
</html>
