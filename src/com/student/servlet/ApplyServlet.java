package com.student.servlet;

import com.student.dao.ActivityDAO;
import com.student.dao.ApplicationDAO;
import com.student.dao.impl.ActivityDAOImpl;
import com.student.dao.impl.ApplicationDAOImpl;
import com.student.entity.Activity;
import com.student.entity.Application;
import com.student.entity.Student;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 活动申请Servlet
 */
public class ApplyServlet extends HttpServlet {
    private ActivityDAO activityDAO = new ActivityDAOImpl();
    private ApplicationDAO applicationDAO = new ApplicationDAOImpl();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // 检查用户是否登录
        HttpSession session = request.getSession();
        Student student = (Student) session.getAttribute("student");
        
        if (student == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String activityIdStr = request.getParameter("activityId");
        if (activityIdStr == null || activityIdStr.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/activities");
            return;
        }
        
        try {
            int activityId = Integer.parseInt(activityIdStr);
            Activity activity = activityDAO.findById(activityId);
            
            if (activity == null) {
                request.setAttribute("error", "活动不存在");
                response.sendRedirect(request.getContextPath() + "/activities");
                return;
            }
            
            // 检查是否已经申请过
            if (applicationDAO.hasApplied(student.getId(), activityId)) {
                request.setAttribute("error", "您已经申请过该活动");
                response.sendRedirect(request.getContextPath() + "/activities");
                return;
            }
            
            // 检查活动是否还有名额
            if (activity.getCurrentParticipants() >= activity.getMaxParticipants()) {
                request.setAttribute("error", "该活动名额已满");
                response.sendRedirect(request.getContextPath() + "/activities");
                return;
            }
            
            request.setAttribute("activity", activity);
            request.setAttribute("student", student);
            request.getRequestDispatcher("/apply.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendRedirect(request.getContextPath() + "/activities");
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // 检查用户是否登录
        HttpSession session = request.getSession();
        Student student = (Student) session.getAttribute("student");
        
        if (student == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String activityIdStr = request.getParameter("activityId");
        String reason = request.getParameter("reason");
        
        if (activityIdStr == null || activityIdStr.trim().isEmpty() ||
            reason == null || reason.trim().isEmpty()) {
            request.setAttribute("error", "活动ID和申请理由不能为空");
            doGet(request, response);
            return;
        }
        
        try {
            int activityId = Integer.parseInt(activityIdStr);
            
            // 再次检查是否已申请
            if (applicationDAO.hasApplied(student.getId(), activityId)) {
                request.setAttribute("error", "您已经申请过该活动");
                response.sendRedirect(request.getContextPath() + "/activities");
                return;
            }
            
            // 创建申请
            Application application = new Application(student.getId(), activityId, reason.trim());
            
            if (applicationDAO.addApplication(application)) {
                // 申请成功
                request.setAttribute("success", "申请提交成功，请等待审核");
                response.sendRedirect(request.getContextPath() + "/status");
            } else {
                // 申请失败
                request.setAttribute("error", "申请提交失败，请重试");
                doGet(request, response);
            }
            
        } catch (NumberFormatException e) {
            request.setAttribute("error", "无效的活动ID");
            response.sendRedirect(request.getContextPath() + "/activities");
        }
    }
}
