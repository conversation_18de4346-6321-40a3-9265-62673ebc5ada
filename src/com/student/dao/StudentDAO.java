package com.student.dao;

import com.student.entity.Student;
import java.util.List;

/**
 * 学生数据访问接口
 */
public interface StudentDAO {
    /**
     * 根据学号和密码查询学生
     */
    Student findByStudentIdAndPassword(String studentId, String password);
    
    /**
     * 根据学号查询学生
     */
    Student findByStudentId(String studentId);
    
    /**
     * 添加学生
     */
    boolean addStudent(Student student);
    
    /**
     * 更新学生信息
     */
    boolean updateStudent(Student student);
    
    /**
     * 根据ID查询学生
     */
    Student findById(int id);
    
    /**
     * 查询所有学生
     */
    List<Student> findAll();
}
