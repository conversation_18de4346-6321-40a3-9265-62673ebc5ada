package com.student.test;

import com.student.util.DBUtil;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 数据库连接测试类
 */
public class DatabaseTest {
    public static void main(String[] args) {
        testConnection();
        testStudentData();
    }
    
    public static void testConnection() {
        System.out.println("=== 测试数据库连接 ===");
        try (Connection conn = DBUtil.getConnection()) {
            System.out.println("✓ 数据库连接成功！");
            System.out.println("数据库URL: " + conn.getMetaData().getURL());
            System.out.println("数据库用户: " + conn.getMetaData().getUserName());
        } catch (SQLException e) {
            System.out.println("✗ 数据库连接失败！");
            e.printStackTrace();
        }
    }
    
    public static void testStudentData() {
        System.out.println("\n=== 测试学生数据 ===");
        String sql = "SELECT * FROM students WHERE student_id = '2021001'";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            if (rs.next()) {
                System.out.println("✓ 找到学生数据：");
                System.out.println("ID: " + rs.getInt("id"));
                System.out.println("学号: " + rs.getString("student_id"));
                System.out.println("姓名: " + rs.getString("name"));
                System.out.println("密码: " + rs.getString("password"));
                System.out.println("邮箱: " + rs.getString("email"));
            } else {
                System.out.println("✗ 未找到学号为2021001的学生数据！");
                
                // 检查表是否存在数据
                String countSql = "SELECT COUNT(*) as count FROM students";
                try (PreparedStatement countStmt = conn.prepareStatement(countSql);
                     ResultSet countRs = countStmt.executeQuery()) {
                    if (countRs.next()) {
                        int count = countRs.getInt("count");
                        System.out.println("学生表中共有 " + count + " 条记录");
                        
                        if (count > 0) {
                            // 显示前几条记录
                            String showSql = "SELECT student_id, name FROM students LIMIT 5";
                            try (PreparedStatement showStmt = conn.prepareStatement(showSql);
                                 ResultSet showRs = showStmt.executeQuery()) {
                                System.out.println("现有学生记录：");
                                while (showRs.next()) {
                                    System.out.println("- 学号: " + showRs.getString("student_id") + 
                                                     ", 姓名: " + showRs.getString("name"));
                                }
                            }
                        }
                    }
                }
            }
            
        } catch (SQLException e) {
            System.out.println("✗ 查询学生数据失败！");
            e.printStackTrace();
        }
    }
}
