package com.student.servlet;

import com.student.dao.StudentDAO;
import com.student.dao.impl.StudentDAOImpl;
import com.student.entity.Student;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 学生登录Servlet
 */
public class LoginServlet extends HttpServlet {
    private StudentDAO studentDAO = new StudentDAOImpl();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 转发到登录页面
        request.getRequestDispatcher("/login.jsp").forward(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String studentId = request.getParameter("studentId");
        String password = request.getParameter("password");
        
        // 参数验证
        if (studentId == null || studentId.trim().isEmpty() ||
            password == null || password.trim().isEmpty()) {
            request.setAttribute("error", "学号和密码不能为空");
            request.getRequestDispatcher("/login.jsp").forward(request, response);
            return;
        }
        
        // 验证用户
        Student student = studentDAO.findByStudentIdAndPassword(studentId.trim(), password);
        
        if (student != null) {
            // 登录成功，将用户信息存入session
            HttpSession session = request.getSession();
            session.setAttribute("student", student);
            session.setAttribute("studentId", student.getId());
            session.setAttribute("studentName", student.getName());
            
            // 重定向到活动列表页面
            response.sendRedirect(request.getContextPath() + "/activities");
        } else {
            // 登录失败
            request.setAttribute("error", "学号或密码错误");
            request.setAttribute("studentId", studentId); // 保留输入的学号
            request.getRequestDispatcher("/login.jsp").forward(request, response);
        }
    }
}
