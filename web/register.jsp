<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>学生活动申请系统 - 注册</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .register-container {
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            width: 500px;
            max-width: 90%;
        }
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .register-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .register-header p {
            color: #666;
            margin: 0;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
            flex: 1;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input:focus, .form-group select:focus {
            border-color: #007bff;
            outline: none;
        }
        .required {
            color: #dc3545;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 15px;
        }
        .btn:hover {
            background-color: #218838;
        }
        .login-link {
            text-align: center;
            margin-top: 20px;
        }
        .login-link a {
            color: #007bff;
            text-decoration: none;
        }
        .login-link a:hover {
            text-decoration: underline;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h1>学生注册</h1>
            <p>创建您的账户</p>
        </div>
        
        <% if (request.getAttribute("error") != null) { %>
            <div class="error">
                <%= request.getAttribute("error") %>
            </div>
        <% } %>
        
        <form action="<%= request.getContextPath() %>/register" method="post">
            <div class="form-row">
                <div class="form-group">
                    <label for="studentId">学号 <span class="required">*</span>:</label>
                    <input type="text" id="studentId" name="studentId" 
                           value="<%= request.getAttribute("studentId") != null ? request.getAttribute("studentId") : "" %>" 
                           required>
                </div>
                
                <div class="form-group">
                    <label for="name">姓名 <span class="required">*</span>:</label>
                    <input type="text" id="name" name="name" 
                           value="<%= request.getAttribute("name") != null ? request.getAttribute("name") : "" %>" 
                           required>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="password">密码 <span class="required">*</span>:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码 <span class="required">*</span>:</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" name="email" 
                       value="<%= request.getAttribute("email") != null ? request.getAttribute("email") : "" %>">
            </div>
            
            <div class="form-group">
                <label for="phone">手机号:</label>
                <input type="tel" id="phone" name="phone" 
                       value="<%= request.getAttribute("phone") != null ? request.getAttribute("phone") : "" %>">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="major">专业:</label>
                    <input type="text" id="major" name="major" 
                           value="<%= request.getAttribute("major") != null ? request.getAttribute("major") : "" %>">
                </div>
                
                <div class="form-group">
                    <label for="grade">年级:</label>
                    <select id="grade" name="grade">
                        <option value="">请选择年级</option>
                        <option value="大一" <%= "大一".equals(request.getAttribute("grade")) ? "selected" : "" %>>大一</option>
                        <option value="大二" <%= "大二".equals(request.getAttribute("grade")) ? "selected" : "" %>>大二</option>
                        <option value="大三" <%= "大三".equals(request.getAttribute("grade")) ? "selected" : "" %>>大三</option>
                        <option value="大四" <%= "大四".equals(request.getAttribute("grade")) ? "selected" : "" %>>大四</option>
                        <option value="研一" <%= "研一".equals(request.getAttribute("grade")) ? "selected" : "" %>>研一</option>
                        <option value="研二" <%= "研二".equals(request.getAttribute("grade")) ? "selected" : "" %>>研二</option>
                        <option value="研三" <%= "研三".equals(request.getAttribute("grade")) ? "selected" : "" %>>研三</option>
                    </select>
                </div>
            </div>
            
            <button type="submit" class="btn">注册</button>
        </form>
        
        <div class="login-link">
            <p>已有账户？ <a href="<%= request.getContextPath() %>/login">立即登录</a></p>
        </div>
    </div>
</body>
</html>
