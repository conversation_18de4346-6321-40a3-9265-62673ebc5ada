package com.student.entity;

import java.util.Date;

/**
 * 申请实体类
 */
public class Application {
    private int id;
    private int studentId;
    private int activityId;
    private String reason;
    private String status; // PENDING, APPROVED, REJECTED
    private Date applyTime;
    private Date reviewTime;
    private String reviewComment;
    
    // 关联对象
    private Student student;
    private Activity activity;
    
    public Application() {}
    
    public Application(int studentId, int activityId, String reason) {
        this.studentId = studentId;
        this.activityId = activityId;
        this.reason = reason;
        this.status = "PENDING";
        this.applyTime = new Date();
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getStudentId() {
        return studentId;
    }
    
    public void setStudentId(int studentId) {
        this.studentId = studentId;
    }
    
    public int getActivityId() {
        return activityId;
    }
    
    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Date getApplyTime() {
        return applyTime;
    }
    
    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }
    
    public Date getReviewTime() {
        return reviewTime;
    }
    
    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }
    
    public String getReviewComment() {
        return reviewComment;
    }
    
    public void setReviewComment(String reviewComment) {
        this.reviewComment = reviewComment;
    }
    
    public Student getStudent() {
        return student;
    }
    
    public void setStudent(Student student) {
        this.student = student;
    }
    
    public Activity getActivity() {
        return activity;
    }
    
    public void setActivity(Activity activity) {
        this.activity = activity;
    }
    
    @Override
    public String toString() {
        return "Application{" +
                "id=" + id +
                ", studentId=" + studentId +
                ", activityId=" + activityId +
                ", reason='" + reason + '\'' +
                ", status='" + status + '\'' +
                ", applyTime=" + applyTime +
                ", reviewTime=" + reviewTime +
                ", reviewComment='" + reviewComment + '\'' +
                '}';
    }
}
