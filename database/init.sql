-- 学生活动申请系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS student_activity DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE student_activity;

-- 创建学生表
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL UNIQUE COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    major VARCHAR(100) COMMENT '专业',
    grade VARCHAR(20) COMMENT '年级',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生表';

-- 创建活动表
CREATE TABLE IF NOT EXISTS activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '活动名称',
    description TEXT COMMENT '活动描述',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    location VARCHAR(200) NOT NULL COMMENT '活动地点',
    max_participants INT NOT NULL DEFAULT 0 COMMENT '最大参与人数',
    current_participants INT NOT NULL DEFAULT 0 COMMENT '当前参与人数',
    organizer VARCHAR(100) NOT NULL COMMENT '主办方',
    status ENUM('OPEN', 'CLOSED', 'CANCELLED') NOT NULL DEFAULT 'OPEN' COMMENT '活动状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动表';

-- 创建申请表
CREATE TABLE IF NOT EXISTS applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL COMMENT '学生ID',
    activity_id INT NOT NULL COMMENT '活动ID',
    reason TEXT NOT NULL COMMENT '申请理由',
    status ENUM('PENDING', 'APPROVED', 'REJECTED') NOT NULL DEFAULT 'PENDING' COMMENT '申请状态',
    apply_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    review_time TIMESTAMP NULL COMMENT '审核时间',
    review_comment TEXT COMMENT '审核意见',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_activity (student_id, activity_id) COMMENT '同一学生不能重复申请同一活动'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='申请表';

-- 插入测试数据

-- 插入测试学生数据
INSERT INTO students (student_id, name, password, email, phone, major, grade) VALUES
('2021001', '张三', '123456', '<EMAIL>', '13800138001', '计算机科学与技术', '大三'),
('2021002', '李四', '123456', '<EMAIL>', '13800138002', '软件工程', '大三'),
('2021003', '王五', '123456', '<EMAIL>', '13800138003', '信息管理与信息系统', '大二'),
('2022001', '赵六', '123456', '<EMAIL>', '13800138004', '电子商务', '大二'),
('2022002', '钱七', '123456', '<EMAIL>', '13800138005', '数据科学与大数据技术', '大二');

-- 插入测试活动数据
INSERT INTO activities (name, description, start_time, end_time, location, max_participants, current_participants, organizer, status) VALUES
('春季运动会', '学校一年一度的春季运动会，包含田径、球类等多项比赛项目。欢迎所有同学积极参与，展现青春活力！', '2025-04-15 08:00:00', '2025-04-15 18:00:00', '学校体育场', 200, 45, '体育部', 'OPEN'),
('编程竞赛', 'ACM程序设计竞赛校内选拔赛，优胜者将代表学校参加省级比赛。适合有编程基础的同学参加。', '2025-04-20 14:00:00', '2025-04-20 17:00:00', '计算机学院机房', 50, 23, '计算机学院', 'OPEN'),
('文艺晚会', '庆祝建校周年文艺晚会，征集各类文艺节目。包括歌曲、舞蹈、小品、相声等形式。', '2025-05-01 19:00:00', '2025-05-01 21:30:00', '学校大礼堂', 100, 67, '学生会文艺部', 'OPEN'),
('学术讲座：人工智能前沿', '邀请知名专家学者讲解人工智能最新发展趋势和应用案例，适合对AI感兴趣的同学参加。', '2025-04-25 15:00:00', '2025-04-25 17:00:00', '学术报告厅', 150, 89, '科研处', 'OPEN'),
('志愿服务活动', '前往社区开展志愿服务活动，包括环境清洁、老人陪伴、儿童辅导等。培养社会责任感。', '2025-04-18 09:00:00', '2025-04-18 16:00:00', '周边社区', 80, 34, '青年志愿者协会', 'OPEN'),
('创业大赛', '大学生创新创业大赛，鼓励同学们展示创新思维和创业项目。设有丰厚奖金和创业扶持。', '2025-05-10 09:00:00', '2025-05-10 17:00:00', '创新创业中心', 60, 28, '创新创业学院', 'OPEN'),
('摄影展览', '学生摄影作品展览活动，展示同学们的摄影才华。同时举办摄影技巧分享会。', '2025-04-22 10:00:00', '2025-04-24 18:00:00', '艺术楼展览厅', 40, 15, '摄影协会', 'OPEN'),
('英语演讲比赛', '提高英语口语表达能力，展示英语学习成果。分为初赛、复赛和决赛三个阶段。', '2025-04-28 14:00:00', '2025-04-28 17:00:00', '外语学院报告厅', 30, 18, '外语学院', 'OPEN');

-- 插入测试申请数据
INSERT INTO applications (student_id, activity_id, reason, status, apply_time, review_time, review_comment) VALUES
(1, 1, '我热爱运动，特别是田径项目。曾在高中时期获得过市级田径比赛第二名，希望能在大学继续发挥特长，为学校争光。', 'APPROVED', '2025-03-15 10:30:00', '2025-03-16 09:15:00', '申请理由充分，运动基础良好，同意参加。'),
(1, 2, '我是计算机专业学生，对编程有浓厚兴趣。平时经常练习算法题，希望通过比赛提升自己的编程能力。', 'PENDING', '2025-03-18 14:20:00', NULL, NULL),
(2, 1, '虽然我不是体育特长生，但我认为运动会是展现团队精神的好机会。我愿意参与组织工作，为活动贡献力量。', 'APPROVED', '2025-03-16 16:45:00', '2025-03-17 11:30:00', '态度积极，同意参加。'),
(2, 3, '我从小学习舞蹈，有一定的舞台表演经验。希望能在文艺晚会上展示才艺，丰富校园文化生活。', 'APPROVED', '2025-03-20 09:10:00', '2025-03-21 14:20:00', '有表演基础，欢迎参加。'),
(3, 4, '作为信息管理专业的学生，我对人工智能在信息系统中的应用很感兴趣，希望通过讲座学习前沿知识。', 'APPROVED', '2025-03-19 11:25:00', '2025-03-20 08:45:00', '学习态度认真，同意参加。'),
(3, 5, '我希望通过志愿服务回馈社会，培养自己的社会责任感。我有耐心，善于与人沟通。', 'PENDING', '2025-03-21 13:40:00', NULL, NULL),
(4, 6, '我有一个关于校园共享单车管理的创业想法，希望通过比赛得到专业指导和资金支持。', 'PENDING', '2025-03-22 15:55:00', NULL, NULL),
(5, 7, '我是摄影爱好者，平时喜欢拍摄校园风景和人文照片。希望能展示作品并学习摄影技巧。', 'APPROVED', '2025-03-17 12:30:00', '2025-03-18 16:10:00', '作品质量不错，欢迎参展。');

-- 创建索引以提高查询性能
CREATE INDEX idx_students_student_id ON students(student_id);
CREATE INDEX idx_activities_status ON activities(status);
CREATE INDEX idx_activities_start_time ON activities(start_time);
CREATE INDEX idx_applications_student_id ON applications(student_id);
CREATE INDEX idx_applications_activity_id ON applications(activity_id);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_applications_apply_time ON applications(apply_time);

-- 显示创建结果
SELECT 'Database initialization completed successfully!' as message;
SELECT COUNT(*) as student_count FROM students;
SELECT COUNT(*) as activity_count FROM activities;
SELECT COUNT(*) as application_count FROM applications;
