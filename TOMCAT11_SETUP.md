# Tomcat 11 + JDK 24 配置指南

## 🚨 重要提示
您当前使用的是 Tomcat 11.0.6 + JDK 24，这需要使用 Jakarta EE API 而不是传统的 Java EE API。

## 📦 需要下载的依赖包

### 1. Jakarta Servlet API
- 下载地址：https://mvnrepository.com/artifact/jakarta.servlet/jakarta.servlet-api/6.0.0
- 文件名：`jakarta.servlet-api-6.0.0.jar`
- 放置位置：`web/WEB-INF/lib/`

### 2. MySQL Connector/J
- 下载地址：https://dev.mysql.com/downloads/connector/j/
- 选择版本：8.0.x 或 9.0.x
- 文件名：`mysql-connector-j-8.x.x.jar`
- 放置位置：`web/WEB-INF/lib/`

## 🔧 IDEA配置步骤

### 1. 项目结构配置
```
File → Project Structure → Modules → Dependencies
点击 + → JARs or directories
添加以下jar包：
- jakarta.servlet-api-6.0.0.jar
- mysql-connector-j-8.x.x.jar
```

### 2. Tom<PERSON>配置
```
Run → Edit Configurations → Tomcat Server → Local
Server选项卡：
- Application server: D:\apache-tomcat-11.0.6\apache-tomcat-11.0.6
- HTTP port: 8080

Deployment选项卡：
- 添加 Artifact: untitled4:war exploded
- Application context: /
```

### 3. 编译设置
```
File → Settings → Build, Execution, Deployment → Compiler → Java Compiler
Project bytecode version: 17 或更高
```

## 🚀 快速解决方案

### 方案A：使用Tomcat 11（当前配置）
1. 下载上述两个jar包到 `web/WEB-INF/lib/`
2. 重新构建项目：`Build → Rebuild Project`
3. 重新部署：停止Tomcat，清理缓存，重新启动

### 方案B：降级到Tomcat 9（推荐新手）
1. 下载 Tomcat 9.0.x：https://tomcat.apache.org/download-90.cgi
2. 在IDEA中重新配置Tomcat路径
3. 使用原始的javax.servlet API（无需修改代码）

## 🔍 验证步骤

### 1. 检查jar包
确保 `web/WEB-INF/lib/` 目录包含：
```
web/WEB-INF/lib/
├── jakarta.servlet-api-6.0.0.jar
└── mysql-connector-j-8.x.x.jar
```

### 2. 检查编译
```bash
# 在IDEA中查看编译输出
Build → Build Project
# 确保没有编译错误
```

### 3. 检查部署
```bash
# 启动Tomcat后检查日志
# 应该看到成功部署的消息，而不是错误
```

## 🐛 常见错误解决

### 错误1：ClassNotFoundException
```
原因：缺少Jakarta Servlet API
解决：下载并添加jakarta.servlet-api-6.0.0.jar
```

### 错误2：过滤器启动失败
```
原因：使用了旧版本的javax.servlet API
解决：确保所有import都是jakarta.servlet.*
```

### 错误3：数据库连接失败
```
原因：缺少MySQL驱动或配置错误
解决：
1. 添加mysql-connector-j-8.x.x.jar
2. 检查DBUtil.java中的数据库配置
3. 确保MySQL服务已启动
```

## 📋 部署检查清单

- [ ] 下载jakarta.servlet-api-6.0.0.jar
- [ ] 下载mysql-connector-j-8.x.x.jar
- [ ] 将jar包放入web/WEB-INF/lib/目录
- [ ] 在IDEA中添加jar包到项目依赖
- [ ] 重新构建项目
- [ ] 检查编译无错误
- [ ] 配置Tomcat运行配置
- [ ] 启动Tomcat测试

## 🎯 测试访问

启动成功后访问：
- 主页：http://localhost:8080/
- 登录页：http://localhost:8080/login
- 测试账号：学号 2021001，密码 123456

如果仍有问题，请提供完整的错误日志！
