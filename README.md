# 学生活动申请系统部署和运行指南

## 项目概述

这是一个基于JavaWeb技术栈开发的学生活动申请系统，学生可以浏览活动、提交申请并查看申请状态。

### 主要功能
- 学生注册和登录
- 浏览可申请的活动列表
- 提交活动申请
- 查看申请状态和审核结果
- 响应式界面设计

### 技术栈
- **后端**: Java Servlet + JSP
- **数据库**: MySQL 8.0+
- **服务器**: Apache Tomcat 9.0+
- **前端**: HTML5 + CSS3 + JavaScript

## 环境要求

### 必需软件
1. **JDK 8 或更高版本**
2. **Apache Tomcat 9.0 或更高版本**
3. **MySQL 8.0 或更高版本**
4. **IntelliJ IDEA** (推荐) 或其他Java IDE

### 推荐配置
- 内存: 4GB 以上
- 硬盘: 2GB 可用空间
- 操作系统: Windows 10/11, macOS, Linux

## 详细部署步骤

### 第一步：安装和配置MySQL数据库

1. **下载并安装MySQL**
   - 访问 [MySQL官网](https://dev.mysql.com/downloads/mysql/)
   - 下载MySQL 8.0版本
   - 安装时设置root密码为 `123456` (或记住您设置的密码)

2. **启动MySQL服务**
   ```bash
   # Windows (以管理员身份运行命令提示符)
   net start mysql
   
   # macOS/Linux
   sudo systemctl start mysql
   ```

3. **创建数据库和表**
   ```bash
   # 登录MySQL
   mysql -u root -p
   # 输入密码: 123456
   
   # 执行初始化脚本
   source /path/to/your/project/database/init.sql
   ```
   
   或者使用MySQL Workbench等图形化工具执行 `database/init.sql` 文件

4. **验证数据库创建**
   ```sql
   USE student_activity;
   SHOW TABLES;
   SELECT COUNT(*) FROM students;
   SELECT COUNT(*) FROM activities;
   ```

### 第二步：配置项目数据库连接

1. **检查数据库配置**
   打开 `src/com/student/util/DBUtil.java` 文件，确认以下配置：
   ```java
   private static final String URL = "*****************************************************************************************************************";
   private static final String USERNAME = "root";
   private static final String PASSWORD = "123456";  // 修改为您的MySQL密码
   ```

2. **如果您的MySQL密码不是123456，请修改PASSWORD字段**

### 第三步：下载MySQL JDBC驱动

1. **下载驱动**
   - 访问 [MySQL Connector/J下载页面](https://dev.mysql.com/downloads/connector/j/)
   - 下载最新版本的mysql-connector-java-x.x.x.jar

2. **添加到项目**
   - 将下载的jar文件复制到 `web/WEB-INF/lib/` 目录下
   - 如果lib目录不存在，请创建它

### 第四步：配置Tomcat服务器

1. **下载Tomcat**
   - 访问 [Apache Tomcat官网](https://tomcat.apache.org/download-90.cgi)
   - 下载Tomcat 9.0版本
   - 解压到合适的目录 (如: `C:\apache-tomcat-9.0.x`)

2. **在IntelliJ IDEA中配置Tomcat**
   - 打开项目
   - 点击 `Run` -> `Edit Configurations`
   - 点击 `+` -> `Tomcat Server` -> `Local`
   - 配置Tomcat安装路径
   - 在 `Deployment` 选项卡中添加项目artifact
   - 设置Application context为 `/` 或 `/student_activity`

### 第五步：编译和部署项目

1. **在IntelliJ IDEA中**
   - 确保项目结构正确
   - 点击 `Build` -> `Build Project`
   - 检查是否有编译错误

2. **手动部署 (可选)**
   ```bash
   # 将整个web目录复制到Tomcat的webapps目录下
   cp -r web/ $TOMCAT_HOME/webapps/student_activity/
   
   # 将编译后的class文件复制到WEB-INF/classes目录
   cp -r out/production/untitled4/* $TOMCAT_HOME/webapps/student_activity/WEB-INF/classes/
   ```

### 第六步：启动和测试

1. **启动Tomcat服务器**
   - 在IntelliJ IDEA中点击运行按钮
   - 或手动启动: `$TOMCAT_HOME/bin/startup.sh` (Linux/Mac) 或 `startup.bat` (Windows)

2. **访问系统**
   - 打开浏览器
   - 访问: `http://localhost:8080/` 或 `http://localhost:8080/student_activity/`
   - 应该自动跳转到登录页面

3. **测试登录**
   使用以下测试账号登录：
   - 学号: `2021001`, 密码: `123456` (张三)
   - 学号: `2021002`, 密码: `123456` (李四)
   - 学号: `2021003`, 密码: `123456` (王五)

## 常见问题解决

### 1. 404错误
**问题**: 访问页面显示404错误
**解决方案**:
- 检查Tomcat是否正常启动
- 确认项目部署路径正确
- 检查web.xml配置是否正确
- 确认servlet映射路径

### 2. 数据库连接失败
**问题**: 页面显示数据库连接错误
**解决方案**:
- 检查MySQL服务是否启动
- 确认数据库用户名密码正确
- 检查数据库URL是否正确
- 确认MySQL JDBC驱动已添加到项目中

### 3. 中文乱码问题
**问题**: 页面显示中文乱码
**解决方案**:
- 确认所有JSP页面都设置了UTF-8编码
- 检查CharacterEncodingFilter是否正常工作
- 确认数据库字符集为utf8mb4

### 4. 编译错误
**问题**: 项目编译失败
**解决方案**:
- 检查JDK版本是否正确
- 确认所有依赖jar包已添加
- 检查项目结构是否正确
- 清理并重新构建项目

## 项目结构说明

```
untitled4/
├── src/
│   └── com/
│       └── student/
│           ├── dao/                 # 数据访问层
│           │   ├── impl/           # DAO实现类
│           │   ├── StudentDAO.java
│           │   ├── ActivityDAO.java
│           │   └── ApplicationDAO.java
│           ├── entity/             # 实体类
│           │   ├── Student.java
│           │   ├── Activity.java
│           │   └── Application.java
│           ├── servlet/            # Servlet控制器
│           │   ├── LoginServlet.java
│           │   ├── RegisterServlet.java
│           │   ├── ActivityListServlet.java
│           │   ├── ApplyServlet.java
│           │   └── ApplicationStatusServlet.java
│           ├── filter/             # 过滤器
│           │   └── CharacterEncodingFilter.java
│           └── util/               # 工具类
│               └── DBUtil.java
├── web/
│   ├── WEB-INF/
│   │   ├── web.xml                # Web应用配置
│   │   └── lib/                   # 依赖jar包目录
│   ├── login.jsp                  # 登录页面
│   ├── register.jsp               # 注册页面
│   ├── activities.jsp             # 活动列表页面
│   ├── apply.jsp                  # 申请页面
│   ├── status.jsp                 # 申请状态页面
│   └── index.jsp                  # 首页
├── database/
│   └── init.sql                   # 数据库初始化脚本
└── README.md                      # 本说明文档
```

## 系统使用说明

### 学生用户操作流程

1. **注册账号**
   - 访问系统首页，点击"立即注册"
   - 填写学号、姓名、密码等信息
   - 提交注册信息

2. **登录系统**
   - 使用学号和密码登录
   - 登录成功后跳转到活动列表页面

3. **浏览活动**
   - 查看所有开放的活动
   - 了解活动详情、时间、地点等信息

4. **申请活动**
   - 点击"立即申请"按钮
   - 填写申请理由
   - 提交申请

5. **查看申请状态**
   - 在"我的申请"页面查看所有申请记录
   - 查看申请状态和审核意见

## 技术支持

如果在部署过程中遇到问题，请检查：

1. **日志文件**
   - Tomcat日志: `$TOMCAT_HOME/logs/catalina.out`
   - 应用日志: 控制台输出

2. **常用调试命令**
   ```bash
   # 检查端口占用
   netstat -an | grep 8080
   
   # 检查MySQL进程
   ps aux | grep mysql
   
   # 检查Tomcat进程
   ps aux | grep tomcat
   ```

3. **重启服务**
   ```bash
   # 重启MySQL
   sudo systemctl restart mysql
   
   # 重启Tomcat
   $TOMCAT_HOME/bin/shutdown.sh
   $TOMCAT_HOME/bin/startup.sh
   ```

## 系统特性

- ✅ 响应式设计，支持多种设备
- ✅ 用户友好的界面
- ✅ 完整的错误处理机制
- ✅ 数据库事务支持
- ✅ 字符编码统一处理
- ✅ 安全的用户认证

---

**祝您使用愉快！如有问题，请仔细检查上述步骤。**
