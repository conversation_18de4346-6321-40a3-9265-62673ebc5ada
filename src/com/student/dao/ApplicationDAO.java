package com.student.dao;

import com.student.entity.Application;
import java.util.List;

/**
 * 申请数据访问接口
 */
public interface ApplicationDAO {
    /**
     * 添加申请
     */
    boolean addApplication(Application application);
    
    /**
     * 根据学生ID查询申请
     */
    List<Application> findByStudentId(int studentId);
    
    /**
     * 根据活动ID查询申请
     */
    List<Application> findByActivityId(int activityId);
    
    /**
     * 检查学生是否已申请某活动
     */
    boolean hasApplied(int studentId, int activityId);
    
    /**
     * 更新申请状态
     */
    boolean updateApplicationStatus(int applicationId, String status, String reviewComment);
    
    /**
     * 根据ID查询申请
     */
    Application findById(int id);
    
    /**
     * 查询所有申请
     */
    List<Application> findAll();
    
    /**
     * 查询待审核的申请
     */
    List<Application> findPendingApplications();
}
