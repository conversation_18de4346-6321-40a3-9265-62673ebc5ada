package com.student.servlet;

import com.student.dao.StudentDAO;
import com.student.dao.impl.StudentDAOImpl;
import com.student.entity.Student;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 学生注册Servlet
 */
public class RegisterServlet extends HttpServlet {
    private StudentDAO studentDAO = new StudentDAOImpl();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 转发到注册页面
        request.getRequestDispatcher("/register.jsp").forward(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String studentId = request.getParameter("studentId");
        String name = request.getParameter("name");
        String password = request.getParameter("password");
        String confirmPassword = request.getParameter("confirmPassword");
        String email = request.getParameter("email");
        String phone = request.getParameter("phone");
        String major = request.getParameter("major");
        String grade = request.getParameter("grade");
        
        // 参数验证
        if (studentId == null || studentId.trim().isEmpty() ||
            name == null || name.trim().isEmpty() ||
            password == null || password.trim().isEmpty() ||
            confirmPassword == null || confirmPassword.trim().isEmpty()) {
            request.setAttribute("error", "必填字段不能为空");
            setRequestAttributes(request, studentId, name, email, phone, major, grade);
            request.getRequestDispatcher("/register.jsp").forward(request, response);
            return;
        }
        
        // 密码确认验证
        if (!password.equals(confirmPassword)) {
            request.setAttribute("error", "两次输入的密码不一致");
            setRequestAttributes(request, studentId, name, email, phone, major, grade);
            request.getRequestDispatcher("/register.jsp").forward(request, response);
            return;
        }
        
        // 检查学号是否已存在
        if (studentDAO.findByStudentId(studentId.trim()) != null) {
            request.setAttribute("error", "该学号已被注册");
            setRequestAttributes(request, studentId, name, email, phone, major, grade);
            request.getRequestDispatcher("/register.jsp").forward(request, response);
            return;
        }
        
        // 创建学生对象
        Student student = new Student(studentId.trim(), name.trim(), password, 
                                    email != null ? email.trim() : "", 
                                    phone != null ? phone.trim() : "", 
                                    major != null ? major.trim() : "", 
                                    grade != null ? grade.trim() : "");
        
        // 保存到数据库
        if (studentDAO.addStudent(student)) {
            // 注册成功
            request.setAttribute("success", "注册成功，请登录");
            request.getRequestDispatcher("/login.jsp").forward(request, response);
        } else {
            // 注册失败
            request.setAttribute("error", "注册失败，请重试");
            setRequestAttributes(request, studentId, name, email, phone, major, grade);
            request.getRequestDispatcher("/register.jsp").forward(request, response);
        }
    }
    
    private void setRequestAttributes(HttpServletRequest request, String studentId, String name, 
                                    String email, String phone, String major, String grade) {
        request.setAttribute("studentId", studentId);
        request.setAttribute("name", name);
        request.setAttribute("email", email);
        request.setAttribute("phone", phone);
        request.setAttribute("major", major);
        request.setAttribute("grade", grade);
    }
}
