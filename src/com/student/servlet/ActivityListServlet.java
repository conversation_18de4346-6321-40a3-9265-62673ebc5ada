package com.student.servlet;

import com.student.dao.ActivityDAO;
import com.student.dao.impl.ActivityDAOImpl;
import com.student.entity.Activity;
import com.student.entity.Student;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 活动列表Servlet
 */
public class ActivityListServlet extends HttpServlet {
    private ActivityDAO activityDAO = new ActivityDAOImpl();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // 检查用户是否登录
        HttpSession session = request.getSession();
        Student student = (Student) session.getAttribute("student");
        
        if (student == null) {
            // 未登录，重定向到登录页面
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        // 获取所有开放的活动
        List<Activity> activities = activityDAO.findOpenActivities();
        
        // 将活动列表设置到request中
        request.setAttribute("activities", activities);
        request.setAttribute("student", student);
        
        // 转发到活动列表页面
        request.getRequestDispatcher("/activities.jsp").forward(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
